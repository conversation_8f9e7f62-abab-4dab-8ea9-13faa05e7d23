{% extends 'base.html' %}

{% block title %}ProFleet - Register{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8 col-lg-6">
        <div class="card shadow">
            <div class="card-header bg-success text-white text-center">
                <h4 class="mb-0">Join ProFleet</h4>
                <small>Create your account and select your role</small>
            </div>
            <div class="card-body">
                {% if form.errors %}
                    <div class="alert alert-danger">
                        <strong>Please correct the following errors:</strong>
                        <ul class="mb-0 mt-2">
                            {% for field, errors in form.errors.items %}
                                {% for error in errors %}
                                    <li>{{ field|title }}: {{ error }}</li>
                                {% endfor %}
                            {% endfor %}
                        </ul>
                    </div>
                {% endif %}

                <form method="post">
                    {% csrf_token %}
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.first_name.id_for_label }}" class="form-label">First Name *</label>
                            {{ form.first_name }}
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.last_name.id_for_label }}" class="form-label">Last Name *</label>
                            {{ form.last_name }}
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.username.id_for_label }}" class="form-label">Username *</label>
                        {{ form.username }}
                        <div class="form-text">Choose a unique username for your account.</div>
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.email.id_for_label }}" class="form-label">Email Address *</label>
                        {{ form.email }}
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.user_type.id_for_label }}" class="form-label">Your Role *</label>
                        {{ form.user_type }}
                        <div class="form-text">{{ form.user_type.help_text }}</div>
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.phone_number.id_for_label }}" class="form-label">Phone Number</label>
                        {{ form.phone_number }}
                        <div class="form-text">Optional - for contact purposes</div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.password1.id_for_label }}" class="form-label">Password *</label>
                            {{ form.password1 }}
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.password2.id_for_label }}" class="form-label">Confirm Password *</label>
                            {{ form.password2 }}
                        </div>
                    </div>

                    <!-- Password Requirements -->
                    <div class="alert alert-info">
                        <small>
                            <strong>Password Requirements:</strong>
                            <ul class="mb-0 mt-1">
                                <li>At least 8 characters long</li>
                                <li>Cannot be too similar to your personal information</li>
                                <li>Cannot be a commonly used password</li>
                                <li>Cannot be entirely numeric</li>
                            </ul>
                        </small>
                    </div>

                    <div class="d-grid">
                        <button type="submit" class="btn btn-success">Create Account</button>
                    </div>
                </form>

                <hr>
                
                <div class="text-center">
                    <p class="mb-0">Already have an account?</p>
                    <a href="{% url 'login' %}" class="btn btn-outline-primary btn-sm">Login Here</a>
                </div>
            </div>
        </div>

        <!-- Role Information -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">Role Information</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <strong>Client:</strong> Track shipments and manage orders<br>
                        <strong>Driver:</strong> Access delivery schedules and routes
                    </div>
                    <div class="col-md-6">
                        <strong>Clearance Agent:</strong> Handle customs documentation<br>
                        <strong>Admin:</strong> System management and analytics
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
