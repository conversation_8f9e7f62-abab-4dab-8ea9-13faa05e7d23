{% extends 'base.html' %}

{% block title %}ProFleet - Client Dashboard{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>Welcome, {{ user.first_name }}!</h2>
            <span class="badge bg-primary fs-6">Client Dashboard</span>
        </div>
    </div>
</div>

<!-- Quick Stats -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white dashboard-card">
            <div class="card-body text-center">
                <h4>12</h4>
                <p class="mb-0">Active Shipments</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white dashboard-card">
            <div class="card-body text-center">
                <h4>45</h4>
                <p class="mb-0">Completed Orders</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white dashboard-card">
            <div class="card-body text-center">
                <h4>3</h4>
                <p class="mb-0">Pending Clearance</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white dashboard-card">
            <div class="card-body text-center">
                <h4>$12,450</h4>
                <p class="mb-0">Monthly Spend</p>
            </div>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="row">
    <div class="col-md-8">
        <!-- Recent Shipments -->
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Recent Shipments</h5>
                <button class="btn btn-primary btn-sm">New Shipment</button>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Tracking #</th>
                                <th>Destination</th>
                                <th>Status</th>
                                <th>ETA</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>PF001234</strong></td>
                                <td>New York, NY</td>
                                <td><span class="badge bg-warning">In Transit</span></td>
                                <td>Dec 15, 2024</td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary">Track</button>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>PF001235</strong></td>
                                <td>Los Angeles, CA</td>
                                <td><span class="badge bg-success">Delivered</span></td>
                                <td>Dec 12, 2024</td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary">View</button>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>PF001236</strong></td>
                                <td>Chicago, IL</td>
                                <td><span class="badge bg-info">Processing</span></td>
                                <td>Dec 18, 2024</td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary">Track</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <button class="btn btn-outline-primary w-100">
                            <i class="fas fa-plus mb-2"></i><br>
                            Create New Order
                        </button>
                    </div>
                    <div class="col-md-4 mb-3">
                        <button class="btn btn-outline-success w-100">
                            <i class="fas fa-search mb-2"></i><br>
                            Track Shipment
                        </button>
                    </div>
                    <div class="col-md-4 mb-3">
                        <button class="btn btn-outline-info w-100">
                            <i class="fas fa-file-invoice mb-2"></i><br>
                            View Invoices
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <!-- Account Information -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Account Information</h5>
            </div>
            <div class="card-body">
                <p><strong>Name:</strong> {{ user.first_name }} {{ user.last_name }}</p>
                <p><strong>Email:</strong> {{ user.email }}</p>
                {% if user.phone_number %}
                    <p><strong>Phone:</strong> {{ user.phone_number }}</p>
                {% endif %}
                <p><strong>Member Since:</strong> {{ user.date_joined|date:"M d, Y" }}</p>
                <button class="btn btn-outline-primary btn-sm">Edit Profile</button>
            </div>
        </div>

        <!-- Notifications -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Recent Notifications</h5>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    <div class="list-group-item border-0 px-0">
                        <small class="text-muted">2 hours ago</small>
                        <p class="mb-0">Shipment PF001234 has departed from warehouse</p>
                    </div>
                    <div class="list-group-item border-0 px-0">
                        <small class="text-muted">1 day ago</small>
                        <p class="mb-0">Invoice #INV-2024-001 is ready for download</p>
                    </div>
                    <div class="list-group-item border-0 px-0">
                        <small class="text-muted">3 days ago</small>
                        <p class="mb-0">Shipment PF001235 delivered successfully</p>
                    </div>
                </div>
                <button class="btn btn-outline-secondary btn-sm w-100 mt-2">View All</button>
            </div>
        </div>

        <!-- Support -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Need Help?</h5>
            </div>
            <div class="card-body text-center">
                <p>Our support team is here to help you.</p>
                <button class="btn btn-primary btn-sm me-2">Contact Support</button>
                <button class="btn btn-outline-primary btn-sm">FAQ</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
{% endblock %}
