{% extends 'base.html' %}

{% block title %}ProFleet - Admin Dashboard{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>Welcome, {{ user.first_name }}!</h2>
            <span class="badge bg-danger fs-6">Admin Dashboard</span>
        </div>
    </div>
</div>

<!-- System Overview -->
<div class="row mb-4">
    <div class="col-md-2">
        <div class="card bg-primary text-white dashboard-card">
            <div class="card-body text-center">
                <h4>{{ total_users }}</h4>
                <p class="mb-0">Total Users</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card bg-info text-white dashboard-card">
            <div class="card-body text-center">
                <h4>{{ clients }}</h4>
                <p class="mb-0">Clients</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card bg-success text-white dashboard-card">
            <div class="card-body text-center">
                <h4>{{ drivers }}</h4>
                <p class="mb-0">Drivers</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card bg-warning text-white dashboard-card">
            <div class="card-body text-center">
                <h4>{{ clearance_agents }}</h4>
                <p class="mb-0">Agents</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card bg-danger text-white dashboard-card">
            <div class="card-body text-center">
                <h4>{{ admins }}</h4>
                <p class="mb-0">Admins</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card bg-secondary text-white dashboard-card">
            <div class="card-body text-center">
                <h4>98.5%</h4>
                <p class="mb-0">Uptime</p>
            </div>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="row">
    <div class="col-md-8">
        <!-- System Management -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">System Management</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <div class="card border-primary">
                            <div class="card-body text-center">
                                <i class="fas fa-users fa-2x text-primary mb-2"></i>
                                <h6>User Management</h6>
                                <a href="/admin/users/customuser/" class="btn btn-primary btn-sm">Manage Users</a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="card border-success">
                            <div class="card-body text-center">
                                <i class="fas fa-chart-bar fa-2x text-success mb-2"></i>
                                <h6>Analytics</h6>
                                <button class="btn btn-success btn-sm">View Reports</button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="card border-warning">
                            <div class="card-body text-center">
                                <i class="fas fa-cog fa-2x text-warning mb-2"></i>
                                <h6>System Settings</h6>
                                <button class="btn btn-warning btn-sm">Configure</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Recent System Activity</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Time</th>
                                <th>User</th>
                                <th>Action</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>10:30 AM</td>
                                <td>john_driver</td>
                                <td>Completed delivery</td>
                                <td><span class="badge bg-success">Success</span></td>
                            </tr>
                            <tr>
                                <td>10:15 AM</td>
                                <td>sarah_client</td>
                                <td>Created new order</td>
                                <td><span class="badge bg-info">Pending</span></td>
                            </tr>
                            <tr>
                                <td>09:45 AM</td>
                                <td>mike_agent</td>
                                <td>Processed clearance</td>
                                <td><span class="badge bg-success">Completed</span></td>
                            </tr>
                            <tr>
                                <td>09:30 AM</td>
                                <td>new_user_123</td>
                                <td>User registration</td>
                                <td><span class="badge bg-warning">Pending Approval</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-2">
                        <button class="btn btn-outline-primary w-100">
                            <i class="fas fa-user-plus mb-1"></i><br>
                            Add User
                        </button>
                    </div>
                    <div class="col-md-3 mb-2">
                        <button class="btn btn-outline-success w-100">
                            <i class="fas fa-download mb-1"></i><br>
                            Export Data
                        </button>
                    </div>
                    <div class="col-md-3 mb-2">
                        <button class="btn btn-outline-warning w-100">
                            <i class="fas fa-database mb-1"></i><br>
                            Backup System
                        </button>
                    </div>
                    <div class="col-md-3 mb-2">
                        <button class="btn btn-outline-info w-100">
                            <i class="fas fa-bell mb-1"></i><br>
                            Send Notice
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <!-- Admin Information -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Admin Information</h5>
            </div>
            <div class="card-body">
                <p><strong>Name:</strong> {{ user.first_name }} {{ user.last_name }}</p>
                <p><strong>Email:</strong> {{ user.email }}</p>
                <p><strong>Admin Level:</strong> Super Admin</p>
                <p><strong>Last Login:</strong> {{ user.last_login|date:"M d, Y H:i" }}</p>
                <button class="btn btn-outline-primary btn-sm">Edit Profile</button>
            </div>
        </div>

        <!-- System Status -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">System Status</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>Server Load</span>
                        <span class="text-success">Normal</span>
                    </div>
                    <div class="progress">
                        <div class="progress-bar bg-success" style="width: 35%"></div>
                    </div>
                </div>
                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>Database</span>
                        <span class="text-success">Healthy</span>
                    </div>
                    <div class="progress">
                        <div class="progress-bar bg-success" style="width: 90%"></div>
                    </div>
                </div>
                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>Storage</span>
                        <span class="text-warning">75% Used</span>
                    </div>
                    <div class="progress">
                        <div class="progress-bar bg-warning" style="width: 75%"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Alerts -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">System Alerts</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-warning alert-sm">
                    <strong>Warning:</strong> Storage approaching 80% capacity
                </div>
                <div class="alert alert-info alert-sm">
                    <strong>Info:</strong> Scheduled maintenance on Sunday 2 AM
                </div>
                <div class="alert alert-success alert-sm">
                    <strong>Success:</strong> Backup completed successfully
                </div>
            </div>
        </div>

        <!-- Quick Links -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Quick Links</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="/admin/" class="btn btn-outline-primary btn-sm">Django Admin</a>
                    <button class="btn btn-outline-success btn-sm">System Logs</button>
                    <button class="btn btn-outline-info btn-sm">Documentation</button>
                    <button class="btn btn-outline-warning btn-sm">Support Tickets</button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
{% endblock %}
