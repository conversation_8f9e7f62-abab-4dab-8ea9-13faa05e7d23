{% extends 'base.html' %}

{% block title %}ProFleet - Clearance Agent Dashboard{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>Welcome, {{ user.first_name }}!</h2>
            <span class="badge bg-warning fs-6">Clearance Agent Dashboard</span>
        </div>
    </div>
</div>

<!-- Quick Stats -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white dashboard-card">
            <div class="card-body text-center">
                <h4>15</h4>
                <p class="mb-0">Pending Clearances</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white dashboard-card">
            <div class="card-body text-center">
                <h4>89</h4>
                <p class="mb-0">Cleared This Month</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white dashboard-card">
            <div class="card-body text-center">
                <h4>6</h4>
                <p class="mb-0">Awaiting Documents</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white dashboard-card">
            <div class="card-body text-center">
                <h4>3</h4>
                <p class="mb-0">Urgent Cases</p>
            </div>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="row">
    <div class="col-md-8">
        <!-- Pending Clearances -->
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Pending Clearances</h5>
                <button class="btn btn-primary btn-sm">New Clearance</button>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Reference #</th>
                                <th>Client</th>
                                <th>Type</th>
                                <th>Priority</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>CLR-2024-001</strong></td>
                                <td>ABC Corp</td>
                                <td>Import</td>
                                <td><span class="badge bg-danger">Urgent</span></td>
                                <td><span class="badge bg-warning">In Review</span></td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary">Process</button>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>CLR-2024-002</strong></td>
                                <td>XYZ Ltd</td>
                                <td>Export</td>
                                <td><span class="badge bg-warning">Medium</span></td>
                                <td><span class="badge bg-info">Documentation</span></td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary">Review</button>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>CLR-2024-003</strong></td>
                                <td>Global Trade Inc</td>
                                <td>Import</td>
                                <td><span class="badge bg-success">Normal</span></td>
                                <td><span class="badge bg-warning">Pending</span></td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary">Start</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Document Management -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Document Management</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Required Documents</h6>
                        <div class="list-group">
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                Commercial Invoice
                                <span class="badge bg-success">✓</span>
                            </div>
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                Bill of Lading
                                <span class="badge bg-success">✓</span>
                            </div>
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                Packing List
                                <span class="badge bg-warning">Pending</span>
                            </div>
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                Certificate of Origin
                                <span class="badge bg-danger">Missing</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6>Quick Actions</h6>
                        <div class="d-grid gap-2">
                            <button class="btn btn-outline-primary">Upload Documents</button>
                            <button class="btn btn-outline-success">Generate Report</button>
                            <button class="btn btn-outline-info">Contact Customs</button>
                            <button class="btn btn-outline-warning">Request Documents</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <!-- Agent Information -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Agent Information</h5>
            </div>
            <div class="card-body">
                <p><strong>Name:</strong> {{ user.first_name }} {{ user.last_name }}</p>
                <p><strong>Agent ID:</strong> CA-{{ user.id|stringformat:"04d" }}</p>
                <p><strong>License:</strong> CBP-12345</p>
                {% if user.phone_number %}
                    <p><strong>Phone:</strong> {{ user.phone_number }}</p>
                {% endif %}
                <p><strong>Specialization:</strong> Import/Export</p>
                <button class="btn btn-outline-primary btn-sm">Edit Profile</button>
            </div>
        </div>

        <!-- Today's Tasks -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Today's Tasks</h5>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    <div class="list-group-item border-0 px-0 d-flex justify-content-between">
                        <span>Review CLR-2024-001</span>
                        <span class="badge bg-danger">Urgent</span>
                    </div>
                    <div class="list-group-item border-0 px-0 d-flex justify-content-between">
                        <span>Submit customs declaration</span>
                        <span class="badge bg-warning">Due Today</span>
                    </div>
                    <div class="list-group-item border-0 px-0 d-flex justify-content-between">
                        <span>Follow up on missing docs</span>
                        <span class="badge bg-info">Pending</span>
                    </div>
                    <div class="list-group-item border-0 px-0 d-flex justify-content-between">
                        <span>Client meeting at 3 PM</span>
                        <span class="badge bg-success">Scheduled</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Customs Updates -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Customs Updates</h5>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    <div class="list-group-item border-0 px-0">
                        <small class="text-muted">2 hours ago</small>
                        <p class="mb-0">New tariff rates effective Jan 1st</p>
                    </div>
                    <div class="list-group-item border-0 px-0">
                        <small class="text-muted">1 day ago</small>
                        <p class="mb-0">Updated documentation requirements</p>
                    </div>
                    <div class="list-group-item border-0 px-0">
                        <small class="text-muted">3 days ago</small>
                        <p class="mb-0">Holiday schedule announced</p>
                    </div>
                </div>
                <button class="btn btn-outline-secondary btn-sm w-100 mt-2">View All Updates</button>
            </div>
        </div>

        <!-- Resources -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Resources</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button class="btn btn-outline-primary btn-sm">Customs Regulations</button>
                    <button class="btn btn-outline-success btn-sm">Tariff Database</button>
                    <button class="btn btn-outline-info btn-sm">Forms Library</button>
                    <button class="btn btn-outline-warning btn-sm">Contact Directory</button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
{% endblock %}
