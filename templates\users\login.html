{% extends 'base.html' %}

{% block title %}ProFleet - Login{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6 col-lg-4">
        <div class="card shadow">
            <div class="card-header bg-primary text-white text-center">
                <h4 class="mb-0">Login to ProFleet</h4>
            </div>
            <div class="card-body">
                {% if form.errors %}
                    <div class="alert alert-danger">
                        <ul class="mb-0">
                            {% for field, errors in form.errors.items %}
                                {% for error in errors %}
                                    <li>{{ error }}</li>
                                {% endfor %}
                            {% endfor %}
                        </ul>
                    </div>
                {% endif %}

                <form method="post">
                    {% csrf_token %}
                    
                    <div class="mb-3">
                        <label for="{{ form.username.id_for_label }}" class="form-label">Username</label>
                        {{ form.username }}
                        {% if form.username.errors %}
                            <div class="text-danger small">
                                {% for error in form.username.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.password.id_for_label }}" class="form-label">Password</label>
                        {{ form.password }}
                        {% if form.password.errors %}
                            <div class="text-danger small">
                                {% for error in form.password.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">Login</button>
                    </div>
                </form>

                <hr>
                
                <div class="text-center">
                    <p class="mb-0">Don't have an account?</p>
                    <a href="{% url 'register' %}" class="btn btn-outline-primary btn-sm">Register Here</a>
                </div>
            </div>
        </div>
        
        <!-- Demo Accounts Info -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">Demo Accounts (for testing)</h6>
            </div>
            <div class="card-body">
                <small class="text-muted">
                    <strong>Note:</strong> You can create accounts with different roles during registration to test the system.
                </small>
            </div>
        </div>
    </div>
</div>
{% endblock %}
