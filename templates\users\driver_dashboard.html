{% extends 'base.html' %}

{% block title %}ProFleet - Driver Dashboard{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>Welcome, {{ user.first_name }}!</h2>
            <span class="badge bg-success fs-6">Driver Dashboard</span>
        </div>
    </div>
</div>

<!-- Quick Stats -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white dashboard-card">
            <div class="card-body text-center">
                <h4>8</h4>
                <p class="mb-0">Today's Deliveries</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white dashboard-card">
            <div class="card-body text-center">
                <h4>156</h4>
                <p class="mb-0">Completed This Month</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white dashboard-card">
            <div class="card-body text-center">
                <h4>2</h4>
                <p class="mb-0">Pending Pickups</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white dashboard-card">
            <div class="card-body text-center">
                <h4>4.8/5</h4>
                <p class="mb-0">Rating</p>
            </div>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="row">
    <div class="col-md-8">
        <!-- Today's Schedule -->
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Today's Schedule</h5>
                <button class="btn btn-success btn-sm">Update Status</button>
            </div>
            <div class="card-body">
                <div class="timeline">
                    <div class="timeline-item">
                        <div class="timeline-marker bg-success"></div>
                        <div class="timeline-content">
                            <h6 class="mb-1">Pickup - Warehouse A</h6>
                            <p class="mb-1">123 Industrial Blvd, City</p>
                            <small class="text-muted">9:00 AM - Completed</small>
                        </div>
                    </div>
                    <div class="timeline-item">
                        <div class="timeline-marker bg-warning"></div>
                        <div class="timeline-content">
                            <h6 class="mb-1">Delivery - Client Location</h6>
                            <p class="mb-1">456 Business St, Downtown</p>
                            <small class="text-muted">11:30 AM - In Progress</small>
                            <button class="btn btn-sm btn-outline-primary mt-2">Mark as Delivered</button>
                        </div>
                    </div>
                    <div class="timeline-item">
                        <div class="timeline-marker bg-secondary"></div>
                        <div class="timeline-content">
                            <h6 class="mb-1">Pickup - Distribution Center</h6>
                            <p class="mb-1">789 Logistics Ave, Industrial Park</p>
                            <small class="text-muted">2:00 PM - Scheduled</small>
                        </div>
                    </div>
                    <div class="timeline-item">
                        <div class="timeline-marker bg-secondary"></div>
                        <div class="timeline-content">
                            <h6 class="mb-1">Delivery - Retail Store</h6>
                            <p class="mb-1">321 Shopping Center, Mall District</p>
                            <small class="text-muted">4:30 PM - Scheduled</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Route Optimization -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Route Information</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Optimized Route</h6>
                        <p class="text-muted">Total Distance: 45.2 miles</p>
                        <p class="text-muted">Estimated Time: 3h 15min</p>
                        <button class="btn btn-primary btn-sm">View Map</button>
                    </div>
                    <div class="col-md-6">
                        <h6>Vehicle Information</h6>
                        <p class="text-muted">Vehicle: Truck #TRK-001</p>
                        <p class="text-muted">Fuel Level: 75%</p>
                        <button class="btn btn-outline-secondary btn-sm">Vehicle Details</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <!-- Driver Information -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Driver Information</h5>
            </div>
            <div class="card-body">
                <p><strong>Name:</strong> {{ user.first_name }} {{ user.last_name }}</p>
                <p><strong>Driver ID:</strong> DRV-{{ user.id|stringformat:"04d" }}</p>
                <p><strong>License:</strong> CDL-A</p>
                {% if user.phone_number %}
                    <p><strong>Phone:</strong> {{ user.phone_number }}</p>
                {% endif %}
                <p><strong>Status:</strong> <span class="badge bg-success">Active</span></p>
                <button class="btn btn-outline-primary btn-sm">Edit Profile</button>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button class="btn btn-success">Check In</button>
                    <button class="btn btn-warning">Report Issue</button>
                    <button class="btn btn-info">Request Break</button>
                    <button class="btn btn-outline-primary">Contact Dispatch</button>
                </div>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Recent Activity</h5>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    <div class="list-group-item border-0 px-0">
                        <small class="text-muted">30 min ago</small>
                        <p class="mb-0">Completed delivery to Downtown Office</p>
                    </div>
                    <div class="list-group-item border-0 px-0">
                        <small class="text-muted">2 hours ago</small>
                        <p class="mb-0">Started route optimization</p>
                    </div>
                    <div class="list-group-item border-0 px-0">
                        <small class="text-muted">This morning</small>
                        <p class="mb-0">Vehicle inspection completed</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Emergency Contact -->
        <div class="card">
            <div class="card-header bg-danger text-white">
                <h5 class="mb-0">Emergency</h5>
            </div>
            <div class="card-body text-center">
                <p>For emergencies, contact dispatch immediately.</p>
                <button class="btn btn-danger">Emergency Contact</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -35px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.timeline-item:not(:last-child)::before {
    content: '';
    position: absolute;
    left: -30px;
    top: 17px;
    width: 2px;
    height: calc(100% + 5px);
    background-color: #dee2e6;
}
</style>
{% endblock %}
